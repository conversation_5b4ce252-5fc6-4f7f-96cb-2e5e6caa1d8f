#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试流式传输修复效果
"""
import asyncio
import json
import websockets
import aiohttp
from datetime import datetime


async def test_streaming_fix():
    """测试流式传输修复效果"""
    base_url = "http://localhost:8000"
    ws_url = "ws://localhost:8000"
    
    print("🧪 测试流式传输修复效果")
    print("=" * 50)
    
    async with aiohttp.ClientSession() as session:
        # 1. 登录获取token
        print("🔐 步骤1: 用户登录...")
        login_data = {
            "code": "test_code_123456",
            "nickname": "测试用户",
            "avatar_url": "https://example.com/avatar.jpg"
        }
        
        async with session.post(f"{base_url}/api/v1/auth/test/login", json=login_data) as response:
            login_result = await response.json()
            if "access_token" not in login_result:
                print(f"❌ 登录失败: {login_result}")
                return
            
            token = login_result["access_token"]
            user_id = login_result["user"]["id"]
            print(f"✅ 登录成功! 用户ID: {user_id}")
        
        # 2. 创建小说
        print("📚 步骤2: 创建小说...")
        headers = {"Authorization": f"Bearer {token}"}
        novel_data = {
            "title": "流式传输测试小说",
            "description": "测试流式传输功能",
            "genre": "fantasy",
            "target_length": 10000
        }
        
        async with session.post(f"{base_url}/api/v1/novels/", json=novel_data, headers=headers) as response:
            novel_result = await response.json()
            if not novel_result.get("success"):
                print(f"❌ 小说创建失败: {novel_result}")
                return
            
            novel_id = novel_result["data"]["id"]
            print(f"✅ 小说创建成功! 小说ID: {novel_id}")
        
        # 3. 启动架构生成任务
        print("🏗️ 步骤3: 启动架构生成...")
        arch_data = {
            "novel_id": novel_id,
            "theme": "一个程序员穿越到魔法世界的冒险故事",
            "genre": "fantasy",
            "target_length": 10000
        }
        
        async with session.post(f"{base_url}/api/v1/generation/architecture", json=arch_data, headers=headers) as response:
            arch_result = await response.json()
            if not arch_result.get("success"):
                print(f"❌ 架构生成启动失败: {arch_result}")
                return
            
            task_id = arch_result["data"]["task_id"]
            print(f"✅ 架构生成任务启动! 任务ID: {task_id}")
        
        # 4. 连接WebSocket监听流式传输
        print("📡 步骤4: 连接WebSocket监听流式传输...")
        
        try:
            websocket_url = f"{ws_url}/ws/{task_id}"
            async with websockets.connect(websocket_url) as websocket:
                print(f"✅ WebSocket连接成功: {websocket_url}")
                
                # 发送心跳包
                heartbeat = {
                    "type": "ping",
                    "timestamp": datetime.now().isoformat()
                }
                await websocket.send(json.dumps(heartbeat))
                
                # 监听消息
                content_chunks = {}  # 存储各阶段的内容片段
                stage_contents = {}  # 存储各阶段的完整内容
                
                async for message in websocket:
                    try:
                        data = json.loads(message)
                        message_type = data.get("type")
                        
                        if message_type == "connection_status":
                            print(f"🔗 连接状态: {data.get('status')}")
                        
                        elif message_type == "pong":
                            print("💓 收到心跳响应")
                        
                        elif message_type == "task_update":
                            status = data.get("status")
                            progress = data.get("progress", 0)
                            current_stage = data.get("current_stage", "")
                            message_text = data.get("message", "")
                            
                            stage_info = f" [{current_stage}]" if current_stage else ""
                            print(f"📈 任务更新{stage_info}: {progress}% - {status} - {message_text}")
                            
                            if status in ["SUCCESS", "FAILED", "CANCELLED"]:
                                print(f"🏁 任务完成，状态: {status}")
                                break
                        
                        elif message_type == "stage_start":
                            stage = data.get("stage")
                            print(f"🎬 开始阶段: {stage}")
                            content_chunks[stage] = []
                        
                        elif message_type == "content_chunk":
                            stage = data.get("stage")
                            chunk = data.get("chunk", "")
                            chunk_index = data.get("chunk_index", 0)
                            total_length = data.get("total_length", 0)
                            
                            if stage not in content_chunks:
                                content_chunks[stage] = []
                            content_chunks[stage].append(chunk)
                            
                            # 显示内容片段（限制长度）
                            display_chunk = chunk[:50] + "..." if len(chunk) > 50 else chunk
                            print(f"📝 [{stage}] 片段{chunk_index}: {display_chunk}")
                            
                            if total_length > 0:
                                print(f"📊 [{stage}] 当前长度: {total_length}字符")
                        
                        elif message_type == "content_complete":
                            stage = data.get("stage")
                            final_content = data.get("final_content", "")
                            
                            stage_contents[stage] = final_content
                            content_length = len(final_content)
                            print(f"✅ 阶段完成: {stage} (共{content_length}字符)")
                            
                            # 显示内容预览
                            if final_content:
                                preview = final_content[:100] + "..." if len(final_content) > 100 else final_content
                                print(f"📄 [{stage}] 内容预览: {preview}")
                        
                        elif message_type == "generation_complete":
                            summary = data.get("summary", "")
                            result = data.get("result", {})
                            
                            print(f"🎉 生成完成!")
                            if summary:
                                print(f"📋 摘要: {summary}")
                            
                            if result and "word_count" in result:
                                print(f"📊 生成字数: {result['word_count']}")
                            
                            break
                        
                        else:
                            print(f"❓ 未知消息类型: {message_type}")
                    
                    except json.JSONDecodeError:
                        print(f"⚠️ 无法解析消息: {message}")
                
                # 总结流式传输结果
                print("\n" + "=" * 50)
                print("📊 流式传输总结:")
                print(f"📝 接收到的阶段数: {len(stage_contents)}")
                
                for stage, content in stage_contents.items():
                    chunk_count = len(content_chunks.get(stage, []))
                    print(f"  - {stage}: {len(content)}字符, {chunk_count}个片段")
                
                if stage_contents:
                    print("✅ 流式传输工作正常!")
                    return True
                else:
                    print("❌ 未接收到任何流式内容")
                    return False
        
        except Exception as e:
            print(f"❌ WebSocket连接失败: {e}")
            return False


if __name__ == "__main__":
    print("🚀 启动流式传输修复测试...")
    try:
        result = asyncio.run(test_streaming_fix())
        if result:
            print("\n🎊 测试成功 - 流式传输工作正常!")
        else:
            print("\n💥 测试失败 - 流式传输存在问题")
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
